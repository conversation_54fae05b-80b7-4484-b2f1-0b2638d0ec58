# PulseOps
Network telemetry and task runner with web UI.

## Docker
```bash
docker compose up --build -d
# visit http://localhost:8765
```

## Native
```bash
cd cmd/pulseops && go build -o pulseops && ./pulseops -config ../../config.sample.yml
```

## Snap
```bash
snapcraft
sudo snap install pulseops_0.2_*.snap --dangerous --classic
pulseops
```

## API
- GET  /api/devices
- GET  /api/metrics/latest?device_id=ID&metric=ping_ms
- GET  /api/metrics?device_id=ID&metric=ping_ms&since=RFC3339&limit=500
- GET  /api/tasks?device_id=ID
- POST /api/tasks {"device_id":ID,"kind":"reboot|refresh_firewall|refresh_wireless","args":"","by":"web"}

## Huawei
Set `platform: huawei` with `user` and `password`. The driver logs in via web API, then attempts reboot across several known endpoints.
