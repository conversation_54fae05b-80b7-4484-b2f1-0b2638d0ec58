<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PulseOps Setup</title>
  <style>
    body { 
      font-family: system-ui, -apple-system, Segoe UI, Roboto, sans-serif; 
      margin: 0; 
      padding: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .setup-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      padding: 3rem;
      width: min(90vw, 420px);
      text-align: center;
    }
    .setup-logo {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2563eb;
      margin-bottom: 0.5rem;
    }
    .setup-subtitle {
      color: #64748b;
      margin-bottom: 2rem;
      font-size: 1.1rem;
    }
    .setup-form {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      text-align: left;
    }
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    .form-group label {
      font-weight: 600;
      color: #374151;
    }
    .form-group input {
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }
    .form-group input:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    .setup-button {
      background: #2563eb;
      color: white;
      border: none;
      padding: 0.875rem 1.5rem;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.2s;
      margin-top: 1rem;
    }
    .setup-button:hover {
      background: #1d4ed8;
    }
    .setup-button:disabled {
      background: #9ca3af;
      cursor: not-allowed;
    }
    .error-message {
      background: #fef2f2;
      border: 1px solid #fecaca;
      color: #dc2626;
      padding: 0.75rem;
      border-radius: 6px;
      font-size: 0.875rem;
      margin-bottom: 1rem;
    }
    .success-message {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      color: #16a34a;
      padding: 0.75rem;
      border-radius: 6px;
      font-size: 0.875rem;
      margin-bottom: 1rem;
    }
    .loading {
      display: none;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      color: #64748b;
      margin-top: 1rem;
    }
    .spinner {
      border: 2px solid #f3f4f6;
      border-top: 2px solid #2563eb;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .setup-info {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 2rem;
      text-align: left;
    }
    .setup-info h3 {
      margin: 0 0 0.5rem 0;
      color: #1e293b;
      font-size: 1rem;
    }
    .setup-info p {
      margin: 0;
      color: #64748b;
      font-size: 0.875rem;
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="setup-container">
    <div class="setup-logo">PulseOps</div>
    <div class="setup-subtitle">Welcome! Let's set up your admin account.</div>
    
    <div class="setup-info">
      <h3>🔐 Admin Account Setup</h3>
      <p>Create your administrator account to get started with PulseOps. All users on this instance will have admin capabilities.</p>
    </div>

    <div id="error-message" class="error-message" style="display: none;"></div>
    <div id="success-message" class="success-message" style="display: none;"></div>

    <form id="setup-form" class="setup-form">
      <div class="form-group">
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autocomplete="username" />
      </div>
      
      <div class="form-group">
        <label for="email">Email (optional)</label>
        <input type="email" id="email" name="email" autocomplete="email" />
      </div>
      
      <div class="form-group">
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required autocomplete="new-password" minlength="6" />
      </div>
      
      <div class="form-group">
        <label for="confirm-password">Confirm Password</label>
        <input type="password" id="confirm-password" name="confirm-password" required autocomplete="new-password" minlength="6" />
      </div>
      
      <button type="submit" class="setup-button" id="setup-button">
        Create Admin Account
      </button>
    </form>

    <div id="loading" class="loading">
      <div class="spinner"></div>
      <span>Setting up your account...</span>
    </div>
  </div>

  <script>
    const setupForm = document.getElementById('setup-form');
    const setupButton = document.getElementById('setup-button');
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');

    function showError(message) {
      errorMessage.textContent = message;
      errorMessage.style.display = 'block';
      successMessage.style.display = 'none';
    }

    function showSuccess(message) {
      successMessage.textContent = message;
      successMessage.style.display = 'block';
      errorMessage.style.display = 'none';
    }

    function hideMessages() {
      errorMessage.style.display = 'none';
      successMessage.style.display = 'none';
    }

    function setLoading(isLoading) {
      if (isLoading) {
        setupForm.style.display = 'none';
        loading.style.display = 'flex';
      } else {
        setupForm.style.display = 'flex';
        loading.style.display = 'none';
      }
    }

    // Check if setup is already completed
    async function checkSetupStatus() {
      try {
        const response = await fetch('/api/auth/setup');
        const data = await response.json();
        if (data.setup_completed) {
          window.location.href = '/';
        }
      } catch (error) {
        console.error('Failed to check setup status:', error);
      }
    }

    setupForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      hideMessages();

      const formData = new FormData(setupForm);
      const username = formData.get('username').trim();
      const email = formData.get('email').trim();
      const password = formData.get('password');
      const confirmPassword = formData.get('confirm-password');

      // Validation
      if (!username) {
        showError('Username is required');
        return;
      }

      if (password.length < 6) {
        showError('Password must be at least 6 characters long');
        return;
      }

      if (password !== confirmPassword) {
        showError('Passwords do not match');
        return;
      }

      setLoading(true);

      try {
        const response = await fetch('/api/auth/setup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username,
            email: email || undefined,
            password,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          showSuccess('Account created successfully! Redirecting...');
          setTimeout(() => {
            window.location.href = '/';
          }, 1500);
        } else {
          const errorText = await response.text();
          showError(errorText || 'Failed to create account');
        }
      } catch (error) {
        showError('Network error. Please try again.');
      } finally {
        setLoading(false);
      }
    });

    // Check setup status on page load
    checkSetupStatus();
  </script>
</body>
</html>
