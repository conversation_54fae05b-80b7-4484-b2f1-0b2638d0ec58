
    body { font-family: system-ui, -apple-system, <PERSON>goe <PERSON>, Roboto, sans-serif; margin: 2rem; }
    code { background: #f5f5f7; padding: 0.2rem 0.4rem; border-radius: 4px; }
    .grid { display: grid; grid-template-columns: repeat(auto-fill,minmax(320px,1fr)); gap: 1rem; }
    .card { border: 1px solid #e5e5e5; border-radius: 8px; padding: 1rem; }
    .app-shell { display: flex; flex-direction: column; gap: 1.5rem; }
    .app-header { display: flex; flex-direction: column; gap: 1rem; }
    .app-header-top { display: flex; justify-content: space-between; gap: 1rem; flex-wrap: wrap; align-items: center; }
    .app-main { display: flex; flex-direction: column; gap: 2rem; }
    .nav-tabs { display: flex; gap: .5rem; flex-wrap: wrap; }
    .nav-tab { padding: 0.45rem 0.9rem; border: 1px solid #d4d4d4; border-radius: 999px; background: #f8fafc; color: #475569; cursor: pointer; font-size: .9rem; transition: background .2s, color .2s, border .2s; }
    .nav-tab:hover { background: #e2e8f0; color: #1e293b; }
    .nav-tab.active { background: #2563eb; color: #fff; border-color: #2563eb; }
    .view-section { display: flex; flex-direction: column; gap: 1rem; }
    .view-section.hidden { display: none !important; }
    .panel { border: 1px solid #e5e5e5; border-radius: 8px; padding: 1.2rem; background: #fff; box-shadow: 0 12px 26px rgba(15, 23, 42, 0.05); }
    .panel h2 { margin-top: 0; margin-bottom: .6rem; }
    .logs-filters { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end; }
    .logs-controls { display: flex; gap: .6rem; flex-wrap: wrap; align-items: center; }
    .logs-list { display: flex; flex-direction: column; gap: .75rem; }
    .log-entry { border: 1px solid #e2e8f0; border-radius: 8px; padding: .9rem 1rem; background: #fff; display: flex; flex-direction: column; gap: .4rem; }
    .log-meta { display: flex; gap: .75rem; flex-wrap: wrap; font-size: .82rem; color: #475569; align-items: center; }
    .log-level { padding: .1rem .5rem; border-radius: 999px; font-size: .75rem; text-transform: uppercase; background: #f1f5f9; color: #334155; }
    .log-level.warn { background: #fef3c7; color: #a16207; }
    .log-level.error { background: #fee2e2; color: #991b1b; }
    .log-level.info { background: #e0f2fe; color: #0369a1; }
    .log-source-pill { padding: .1rem .45rem; border-radius: 999px; font-size: .75rem; background: #ede9fe; color: #5b21b6; }
    .log-source-pill.system { background: #f8fafc; color: #0f172a; border: 1px solid #cbd5f5; }
    .log-device { font-weight: 600; color: #1f2937; }
    .log-message { font-size: .95rem; color: #111827; word-break: break-word; }
    .log-context { display: flex; flex-wrap: wrap; gap: .5rem; font-size: .8rem; color: #475569; }
    .log-context span { background: #f1f5f9; padding: .2rem .45rem; border-radius: 6px; }
    .device-toolbar { display: flex; gap: .75rem; flex-wrap: wrap; align-items: center; justify-content: space-between; }
    .device-toolbar-buttons { display: flex; gap: .5rem; flex-wrap: wrap; }
    .device-table { width: 100%; border-collapse: collapse; border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden; }
    .device-table thead { background: #f8fafc; }
    .device-table th, .device-table td { padding: .6rem .75rem; text-align: left; border-bottom: 1px solid #e2e8f0; font-size: .9rem; }
    .device-table th { font-size: .78rem; text-transform: uppercase; letter-spacing: .04em; color: #475569; }
    .device-table tbody tr:hover { background: #f1f5f9; }
    .settings-form { display: flex; flex-direction: column; gap: 1.5rem; max-width: 640px; }
    .settings-group { display: flex; flex-direction: column; gap: .8rem; }
    .settings-group h3 { margin: 0; font-size: 1.05rem; }
    .settings-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 1rem; }
    .settings-field { display: flex; flex-direction: column; gap: .4rem; }
    .settings-field label { font-weight: 600; color: #1f2937; }
    .settings-field input, .settings-field select { padding: .5rem .6rem; border: 1px solid #d4d4d8; border-radius: 6px; font-size: .95rem; }
    .settings-field input:focus, .settings-field select:focus { outline: none; border-color: #2563eb; box-shadow: 0 0 0 2px rgba(37,99,235,0.15); }
    .settings-note { font-size: .8rem; color: #64748b; }
    .settings-actions { display: flex; gap: .75rem; align-items: center; }
    .insights-header { display: flex; flex-wrap: wrap; gap: .75rem; align-items: center; }
    .insights-summary { display: flex; flex-direction: column; gap: .3rem; }
    .insights-meta { display: flex; gap: 1.5rem; flex-wrap: wrap; font-size: .88rem; color: #475569; }
    .insights-chart { background: #fff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; }
    .empty-state { padding: 1.2rem; border: 1px dashed #cbd5f5; border-radius: 8px; background: #f8fafc; color: #475569; font-size: .9rem; }
    .form-inline { display: flex; gap: .5rem; align-items: center; flex-wrap: wrap; }
    .muted-strong { color: #475569; font-weight: 500; }
    .sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0; }
    .row { display: flex; gap: .5rem; align-items:center; }
    button { padding: .4rem .7rem; border: 1px solid #ddd; border-radius: 6px; background: #fafafa; cursor: pointer; }
    button:hover { background: #f0f0f0; }
    button.danger { background: #ffeaea; border-color: #f5b5b5; color: #a31616; }
    button.danger:hover { background: #ffd6d6; }
    button:disabled { opacity: .6; cursor: not-allowed; }
    .muted { color: #666; font-size: .9rem; }
    .latest-metrics { display: grid; gap: .3rem; }
    .metrics-row { display: grid; grid-template-columns: repeat(2, minmax(120px, 1fr)); gap: .4rem; }
    .metrics-header .metric-cell { font-size: .78rem; text-transform: uppercase; letter-spacing: .04em; color: #475569; }
    .metrics-values .metric-cell { font-size: .95rem; color: #1f2937; font-weight: 600; }
    .metric-cell { padding: .1rem 0; }
    .device-logs { margin-top: .6rem; border-top: 1px solid #e2e8f0; padding-top: .6rem; display: flex; flex-direction: column; gap: .35rem; }
    .device-log-header { display: flex; justify-content: space-between; align-items: center; font-size: .82rem; color: #475569; }
    .device-log-toggle { background: transparent; border: none; color: #2563eb; font-size: .82rem; cursor: pointer; padding: 0; }
    .device-log-toggle:hover { text-decoration: underline; }
    .device-log-list { display: flex; flex-direction: column; gap: .35rem; }
    .device-log-entry { display: grid; grid-template-columns: 96px auto; gap: .5rem; font-size: .85rem; color: #334155; }
    .device-log-time { color: #64748b; font-family: ui-monospace, SFMono-Regular, Menlo, monospace; font-size: .8rem; }
    .device-log-level { text-transform: uppercase; font-weight: 600; margin-right: .4rem; font-size: .75rem; color: #475569; }
    .device-log-message { word-break: break-word; }
    .device-log-body { display: flex; gap: .4rem; }
    .mono { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; font-size: .9rem; }
    .hidden { display: none !important; }
    .btn { padding: 0.6rem 1rem; border-radius: 6px; border: 1px solid transparent; font-size: .95rem; cursor: pointer; transition: background .2s, color .2s, border .2s; }
    .btn-primary { background: #007bff; border-color: #007bff; color: #fff; }
    .btn-primary:hover { background: #005dc1; border-color: #0050a3; }
    .btn-secondary { background: #6c757d; border-color: #6c757d; color: #fff; }
    .btn-secondary:hover { background: #5b6269; border-color: #545a60; }
    .btn-outline { background: transparent; border-color: #007bff; color: #007bff; }
    .btn-outline:hover { background: #007bff; color: #fff; }
    .btn:disabled { opacity: .6; cursor: not-allowed; }
    .modal-backdrop { position: fixed; inset: 0; background: rgba(0,0,0,0.35); display: flex; align-items: center; justify-content: center; z-index: 1000; }
    .modal-backdrop.hidden { display: none; }
    .modal { background: #fff; border-radius: 8px; padding: 1.5rem; max-width: 420px; width: min(90vw, 420px); box-shadow: 0 18px 48px rgba(0,0,0,0.18); display: flex; flex-direction: column; gap: 1rem; }
    .modal-actions { display: flex; justify-content: flex-end; gap: .5rem; }
    .toast-container { position: fixed; bottom: 1.5rem; right: 1.5rem; display: flex; flex-direction: column; gap: .5rem; z-index: 1100; }
    .toast { background: #333; color: #fff; padding: .75rem 1rem; border-radius: 6px; box-shadow: 0 8px 24px rgba(0,0,0,0.25); display: flex; align-items: center; gap: .75rem; }
    .toast button { background: rgba(255,255,255,0.15); border-color: rgba(255,255,255,0.2); color: #fff; }
    .toast button:hover { background: rgba(255,255,255,0.25); }
    .toast.success { background: #1d7a46; }
    .toast.error { background: #a31616; }
    .toast.info { background: #2563eb; }
    .menu-wrapper { position: relative; margin-left: auto; }
    .menu-trigger { background: transparent; border: none; font-size: 1.2rem; line-height: 1; padding: .2rem .4rem; cursor: pointer; color: #555; }
    .menu-trigger:hover { background: #f0f0f0; border-radius: 6px; }
    .menu-list { position: absolute; top: calc(100% + .3rem); right: 0; background: #fff; border: 1px solid #ddd; border-radius: 8px; min-width: 160px; box-shadow: 0 10px 30px rgba(0,0,0,0.12); display: flex; flex-direction: column; overflow: hidden; z-index: 950; }
    .menu-list.hidden { display: none; }
    .menu-item { padding: .6rem .75rem; border: none; background: transparent; text-align: left; cursor: pointer; font-size: .95rem; color: #333; }
    .menu-item:hover { background: #f5f5f5; }
    .menu-item.danger { color: #a31616; }
    .menu-item.danger:hover { background: #ffeaea; }
    .drawer-backdrop { position: fixed; inset: 0; background: rgba(0,0,0,0.45); display: none; align-items: stretch; justify-content: flex-end; z-index: 1200; }
    .drawer-backdrop.active { display: flex; }
    .drawer-panel { width: min(640px, 100%); max-width: 640px; background: #fff; height: 100%; display: flex; flex-direction: column; box-shadow: -4px 0 24px rgba(0,0,0,0.18); }
    .drawer-header { padding: 1.5rem; border-bottom: 1px solid #e5e5e5; display: flex; justify-content: space-between; align-items: flex-start; gap: 1rem; }
    .drawer-title { margin: 0; font-size: 1.4rem; }
    .drawer-body { padding: 1.5rem; overflow-y: auto; flex: 1; }
    .drawer-footer { padding: 1.25rem 1.5rem; border-top: 1px solid #e5e5e5; display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
    .drawer-close { background: transparent; border: none; font-size: 1.5rem; cursor: pointer; line-height: 1; color: #666; }
    .drawer-close:hover { color: #000; }
    .stepper { display: flex; gap: 0.5rem; margin-bottom: 1.5rem; }
    .stepper-item { flex: 1; padding: 0.6rem 0.75rem; border-radius: 999px; background: #f0f0f0; color: #666; font-size: .9rem; text-align: center; }
    .stepper-item.active { background: #007bff; color: #fff; }
    .stepper-item.completed { background: #28a745; color: #fff; }
    .edit-step { display: none; }
    .edit-step.active { display: block; }
    .form-group { margin-bottom: 1.1rem; display: flex; flex-direction: column; gap: .4rem; }
    .form-group label { font-weight: 600; }
    .form-group input, .form-group select, .form-group textarea { padding: 0.6rem 0.7rem; border: 1px solid #d4d4d4; border-radius: 6px; font-size: .95rem; }
    .form-group textarea { min-height: 100px; resize: vertical; }
    .form-group .help { font-size: .85rem; color: #666; }
    .form-group .error { font-size: .85rem; color: #c0392b; }
    .ssh-key-field-controls { display: flex; gap: .5rem; align-items: center; }
    .ssh-key-field-controls select { flex: 1; }
    .badge { display: inline-flex; align-items: center; gap: .3rem; padding: .3rem .55rem; border-radius: 999px; font-size: .72rem; font-weight: 600; text-transform: uppercase; letter-spacing: .05em; background: #f1f5f9; color: #0f172a; }
    .badge-label { display: inline-block; }
    .badge-icon { font-size: .9rem; line-height: 1; }
    .badge-router { background: #e8f4ff; color: #0b5394; }
    .badge-switch { background: #f3e8ff; color: #6f42c1; }
    .badge-ap { background: #e6f9ff; color: #046c8c; }
    .badge-firewall { background: #fde2e1; color: #a1251b; }
    .badge-server { background: #e8f5e9; color: #1b5e20; }
    .badge-gateway { background: #fff4d6; color: #8a4b00; }
    .badge-modem { background: #f1f0ff; color: #4338ca; }
    .badge-default { background: #eceff1; color: #37474f; }
    .edit-loading { display: flex; flex-direction: column; align-items: center; gap: .75rem; padding: 2rem 0; color: #555; }
    .spinner { border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 28px; height: 28px; animation: spin 1s linear infinite; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    .validation-results { display: flex; flex-direction: column; gap: .5rem; }
    .validation-item { padding: .6rem .75rem; border-radius: 6px; font-size: .95rem; }
    .validation-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .validation-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .validation-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .edit-summary { background: #f7f8fa; border: 1px solid #e4e7ea; border-radius: 8px; padding: 1rem; display: flex; flex-direction: column; gap: .4rem; }
    .key-modal-backdrop { position: fixed; inset: 0; background: rgba(0,0,0,0.5); display: none; align-items: center; justify-content: center; z-index: 1300; padding: 1.5rem; }
    .key-modal-backdrop.active { display: flex; }
    .key-modal { width: min(560px, 95vw); background: #fff; border-radius: 10px; box-shadow: 0 18px 48px rgba(0,0,0,0.2); display: flex; flex-direction: column; max-height: 90vh; }
    .key-modal header { padding: 1.25rem 1.5rem; border-bottom: 1px solid #ececec; display: flex; justify-content: space-between; align-items: center; }
    .key-modal .modal-body { padding: 1.5rem; overflow-y: auto; display: flex; flex-direction: column; gap: 1.25rem; }
    .key-list { display: flex; flex-direction: column; gap: .75rem; }
    .key-card { border: 1px solid #e3e3e3; border-radius: 8px; padding: .9rem; display: flex; justify-content: space-between; gap: 1rem; align-items: center; }
    .key-card-actions { display: flex; gap: .5rem; }
    .key-viewer { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; background: #f4f4f4; border-radius: 6px; padding: .75rem; white-space: pre-wrap; word-break: break-all; }
    .task-section { margin-top: 1rem; padding-top: .8rem; border-top: 1px solid #e5e5e5; display: flex; flex-direction: column; gap: .6rem; }
    .task-section h4 { margin: 0; font-size: 1rem; }
    .task-list { display: flex; flex-direction: column; gap: .5rem; }
    .task-item { border: 1px solid #e5e5e5; border-radius: 6px; background: #fafafa; padding: .6rem .75rem; display: flex; flex-direction: column; gap: .4rem; }
    .task-meta { display: flex; justify-content: space-between; gap: .5rem; align-items: flex-start; }
    .status-pill { padding: .2rem .55rem; border-radius: 999px; font-size: .75rem; text-transform: uppercase; letter-spacing: .05em; }
    .status-queued { background: #fff3cd; color: #856404; }
    .status-running { background: #d0ebff; color: #084c91; }
    .status-done { background: #d4edda; color: #155724; }
    .status-error { background: #f8d7da; color: #721c24; }
    .task-output { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; font-size: .8rem; color: #555; white-space: pre-wrap; max-height: 6rem; overflow: hidden; }
    .btn-danger { background: #d93025; border-color: #d93025; color: #fff; }
    .btn-danger:hover { background: #b6261c; border-color: #a71f16; }
    .task-toggle { align-self: flex-start; padding: .3rem .65rem; font-size: .8rem; border-radius: 6px; border: 1px solid #007bff; background: transparent; color: #007bff; cursor: pointer; }
    .task-toggle:hover { background: #007bff; color: #fff; }
    .device-header { align-items: flex-start; gap: .75rem; }
    .device-header-info { display: flex; flex-direction: column; gap: .2rem; min-width: 0; }
    .device-title-row { display: flex; align-items: center; gap: .6rem; flex-wrap: wrap; }
    .device-name { font-size: 1.05rem; }
    .device-subtitle { font-size: .8rem; }
    .modal-actions { display: flex; gap: .5rem; align-items: center; justify-content: flex-end; flex-wrap: wrap; }
    .modal-actions-extra { display: flex; gap: .5rem; margin-right: auto; flex-wrap: wrap; }
    .modal-actions-main { display: flex; gap: .5rem; align-items: center; }
    .confirm-extra-block { display: flex; flex-direction: column; gap: .4rem; }
    .confirm-extra-block label { font-size: .8rem; color: #555; }
    .confirm-extra-block input { padding: .45rem .6rem; border: 1px solid #d4d4d4; border-radius: 6px; font-size: .9rem; min-width: 220px; }
    .confirm-extra-block input:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0,123,255,0.25); }
    .metric-chart { margin-top: .6rem; }
    .metric-chart canvas { width: 100%; height: 140px; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; }
  