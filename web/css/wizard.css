
    body { font-family: system-ui, -apple-system, <PERSON>goe <PERSON>I, Roboto, sans-serif; margin: 2rem; background: #f8f9fa; }
    .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
    .header { padding: 2rem; border-bottom: 1px solid #e5e5e5; text-align: center; }
    .content { padding: 2rem; }
    .step { display: none; }
    .step.active { display: block; }
    .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
    .step-item { padding: 0.5rem 1rem; margin: 0 0.5rem; border-radius: 20px; background: #e5e5e5; color: #666; }
    .step-item.active { background: #007bff; color: white; }
    .step-item.completed { background: #28a745; color: white; }
    .form-group { margin-bottom: 1.5rem; }
    .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
    .form-group input, .form-group select, .form-group textarea { 
      width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 1rem; 
    }
    .form-group input:focus, .form-group select:focus { outline: none; border-color: #007bff; box-shadow: 0 0 0 2px rgba(0,123,255,0.25); }
    .form-group .help { font-size: 0.875rem; color: #666; margin-top: 0.25rem; }
    .form-group .error { font-size: 0.875rem; color: #dc3545; margin-top: 0.25rem; }
    .badge { display: inline-flex; align-items: center; gap: .3rem; padding: .3rem .55rem; border-radius: 999px; font-size: .72rem; font-weight: 600; text-transform: uppercase; letter-spacing: .05em; background: #f1f5f9; color: #0f172a; }
    .badge-icon { font-size: .9rem; line-height: 1; }
    .badge-label { display: inline-block; }
    .badge-router { background: #e8f4ff; color: #0b5394; }
    .badge-switch { background: #f3e8ff; color: #6f42c1; }
    .badge-ap { background: #e6f9ff; color: #046c8c; }
    .badge-firewall { background: #fde2e1; color: #a1251b; }
    .badge-server { background: #e8f5e9; color: #1b5e20; }
    .badge-gateway { background: #fff4d6; color: #8a4b00; }
    .badge-modem { background: #f1f0ff; color: #4338ca; }
    .badge-default { background: #eceff1; color: #37474f; }
    .template-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem; }
    .template-card { border: 2px solid #e5e5e5; border-radius: 8px; padding: 1rem; cursor: pointer; transition: all 0.2s; }
    .template-card:hover { border-color: #007bff; }
    .template-card.selected { border-color: #007bff; background: #f0f8ff; }
    .template-card-header { display: flex; align-items: center; justify-content: space-between; gap: .75rem; margin-bottom: .6rem; }
    .template-card h3 { margin: 0; font-size: 1.1rem; }
    .template-meta { display: flex; gap: .75rem; font-size: .8rem; color: #555; margin-top: .6rem; }
    .template-card p { margin: 0; color: #666; font-size: 0.875rem; }
    .mode-toggle { display: flex; gap: 0.5rem; margin: 1.5rem 0 1rem; }
    .mode-toggle .mode-btn { flex: 1; }
    .discovery-section { background: #f8f9fa; border-radius: 8px; padding: 1.5rem; margin: 1rem 0; }
    .device-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 1rem; margin-top: 1rem; }
    .device-card { border: 1px solid #ddd; border-radius: 8px; padding: 1rem; background: white; cursor: pointer; transition: all 0.2s; }
    .device-card:hover { border-color: #007bff; }
    .device-card.selected { border-color: #007bff; background: #f0f8ff; }
    .device-info { display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem; }
    .device-ip { font-weight: 500; }
    .device-ping { color: #28a745; font-size: 0.875rem; }
    .device-services { display: flex; flex-wrap: wrap; gap: 0.25rem; }
    .service-tag { background: #e9ecef; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; }
    .suggestions { margin-top: 0.5rem; }
    .suggestion-tag { background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; margin-right: 0.25rem; }
    .buttons { display: flex; justify-content: space-between; margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #e5e5e5; }
    .btn { padding: 0.75rem 1.5rem; border: none; border-radius: 6px; font-size: 1rem; cursor: pointer; transition: all 0.2s; }
    .btn-primary { background: #007bff; color: white; }
    .btn-primary:hover { background: #0056b3; }
    .btn-secondary { background: #6c757d; color: white; }
    .btn-secondary:hover { background: #545b62; }
    .btn-outline { background: transparent; color: #007bff; border: 1px solid #007bff; }
    .btn-outline:hover { background: #007bff; color: white; }
    .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    .loading { text-align: center; padding: 2rem; }
    .spinner { border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto 1rem; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    .validation-results { margin-top: 1rem; }
    .validation-item { padding: 0.5rem; margin: 0.25rem 0; border-radius: 4px; }
    .validation-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .validation-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .validation-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .hidden { display: none; }
    .ssh-key-field-controls { display: flex; gap: 0.5rem; align-items: center; margin-bottom: 0.75rem; }
    .ssh-key-field-controls select { flex: 1; }
    #ssh-key-path-input { margin-top: 0.75rem; }
    .modal-overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.55); display: none; align-items: center; justify-content: center; padding: 1.5rem; z-index: 1000; }
    .modal-overlay.active { display: flex; }
    .modal { background: white; border-radius: 10px; width: min(720px, 95%); max-height: 92vh; display: flex; flex-direction: column; box-shadow: 0 20px 45px rgba(15,23,42,0.25); }
    .modal-header { display: flex; justify-content: space-between; align-items: center; padding: 1rem 1.5rem; border-bottom: 1px solid #e5e5e5; }
    .modal-body { padding: 1.5rem; overflow-y: auto; }
    .key-list { display: flex; flex-direction: column; gap: 0.75rem; margin-bottom: 1.5rem; }
    .key-item { border: 1px solid #dedede; border-radius: 8px; padding: 0.75rem 1rem; display: flex; justify-content: space-between; align-items: center; gap: 1rem; }
    .key-meta { display: flex; flex-direction: column; gap: 0.15rem; }
    .key-meta span { font-size: 0.9rem; color: #555; }
    .key-actions { display: flex; gap: 0.5rem; }
    .key-actions .btn { padding: 0.5rem 0.75rem; font-size: 0.9rem; }
    .key-add { border-top: 1px solid #e5e5e5; padding-top: 1.5rem; display: flex; flex-direction: column; gap: 0.75rem; }
    .key-add textarea { min-height: 140px; font-family: ui-monospace, SFMono-Regular, SFMono, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    .key-viewer { background: #f8f9fa; border-radius: 6px; padding: 1rem; font-family: ui-monospace, monospace; font-size: 0.85rem; white-space: pre-wrap; word-break: break-all; }
    .key-empty { color: #666; font-size: 0.9rem; }
  