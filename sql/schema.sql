PRAGMA journal_mode=WAL;

CREATE TABLE IF NOT EXISTS devices (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,
  host TEXT NOT NULL,
  kind TEXT NOT NULL,
  platform TEXT NOT NULL,
  user TEXT,
  ssh_key TEXT,
  meta TEXT
);

CREATE TABLE IF NOT EXISTS metrics (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  device_id INTEGER NOT NULL,
  ts DATETIME NOT NULL,
  metric TEXT NOT NULL,
  value REAL,
  unit TEXT,
  raw TEXT,
  FOREIGN KEY(device_id) REFERENCES devices(id)
);

CREATE TABLE IF NOT EXISTS tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  device_id INTEGER NOT NULL,
  kind TEXT NOT NULL,
  args TEXT,
  requested_by TEXT,
  requested_at DATETIME NOT NULL,
  status TEXT NOT NULL,
  output TEXT,
  FOREI<PERSON><PERSON> KEY(device_id) REFERENCES devices(id)
);

CREATE TABLE IF NOT EXISTS pending_device_deletions (
  device_id INTEGER PRIMARY KEY,
  delete_at TEXT NOT NULL,
  FOREIGN KEY(device_id) REFERENCES devices(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS deleted_devices (
  name TEXT PRIMARY KEY,
  deleted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
