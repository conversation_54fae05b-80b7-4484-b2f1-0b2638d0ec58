package templates

import (
	"github.com/pulseops/pulseops/internal/config"
)

// GetAllTemplates returns all available device templates
func GetAllTemplates() []config.DeviceTemplate {
	return []config.DeviceTemplate{
		getOpenWrtTemplate(),
		getEdgeOSTemplate(),
		getHuaweiTemplate(),
		getGenericRouterTemplate(),
		getGenericPrinterTemplate(),
		getGenericServerTemplate(),
		getUnifiTemplate(),
		getMikroTikTemplate(),
	}
}

// GetTemplateByID returns a template by its ID
func GetTemplateByID(id string) *config.DeviceTemplate {
	for _, template := range GetAllTemplates() {
		if template.ID == id {
			return &template
		}
	}
	return nil
}

// GetTemplatesByKind returns templates filtered by device kind
func GetTemplatesByKind(kind string) []config.DeviceTemplate {
	var result []config.DeviceTemplate
	for _, template := range GetAllTemplates() {
		if template.Kind == kind {
			result = append(result, template)
		}
	}
	return result
}

func getOpenWrtTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "openwrt",
		Name:        "OpenWrt Router",
		Description: "OpenWrt-based router or access point",
		Kind:        "router",
		Platform:    "openwrt",
		DefaultUser: "root",
		RequiresSSH: true,
		RequiresPassword: false,
		DefaultPorts: []int{22, 80, 443},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., main-router",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "***********",
				Help:        "IP address or hostname of the device",
			},
			{
				Name:        "user",
				Label:       "SSH Username",
				Type:        "text",
				Required:    true,
				Default:     "root",
				Help:        "SSH username (usually 'root' for OpenWrt)",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path",
				Type:        "file",
				Required:    true,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; change if the device listens on a custom SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{22},
			TestCommands:  []string{"uname -a", "cat /etc/openwrt_release"},
		},
	}
}

func getEdgeOSTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "edgeos",
		Name:        "EdgeOS Router",
		Description: "Ubiquiti EdgeRouter running EdgeOS",
		Kind:        "router",
		Platform:    "edgeos",
		DefaultUser: "ubnt",
		RequiresSSH: true,
		RequiresPassword: false,
		DefaultPorts: []int{22, 80, 443},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., edge-router",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "***********",
				Help:        "IP address or hostname of the device",
			},
			{
				Name:        "user",
				Label:       "SSH Username",
				Type:        "text",
				Required:    true,
				Default:     "ubnt",
				Help:        "SSH username (usually 'ubnt' for EdgeOS)",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path",
				Type:        "file",
				Required:    true,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; change if the device listens on a custom SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{22},
			TestCommands:  []string{"show version"},
		},
	}
}

func getHuaweiTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "huawei",
		Name:        "Huawei Modem",
		Description: "Huawei mobile broadband modem",
		Kind:        "modem",
		Platform:    "huawei",
		DefaultUser: "admin",
		RequiresSSH: false,
		RequiresPassword: true,
		DefaultPorts: []int{80, 8080},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., huawei-modem",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Default:     "***********",
				Placeholder: "***********",
				Help:        "IP address of the modem (usually ***********)",
			},
			{
				Name:        "user",
				Label:       "Username",
				Type:        "text",
				Required:    true,
				Default:     "admin",
				Help:        "Web interface username",
			},
			{
				Name:        "password",
				Label:       "Password",
				Type:        "password",
				Required:    true,
				Help:        "Web interface password",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{80},
		},
	}
}

func getGenericRouterTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "generic-router",
		Name:        "Generic Router",
		Description: "Generic router or network device",
		Kind:        "router",
		Platform:    "generic",
		DefaultUser: "admin",
		RequiresSSH: false,
		RequiresPassword: false,
		DefaultPorts: []int{80, 443},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., router-1",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "***********",
				Help:        "IP address or hostname of the device",
			},
			{
				Name:        "user",
				Label:       "Username (optional)",
				Type:        "text",
				Required:    false,
				Help:        "Username if SSH access is available",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path (optional)",
				Type:        "file",
				Required:    false,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file if SSH access is available",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; set a custom value if the device uses a non-standard SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{80},
		},
	}
}

func getGenericPrinterTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "generic-printer",
		Name:        "Network Printer",
		Description: "Network-connected printer",
		Kind:        "printer",
		Platform:    "generic",
		DefaultUser: "",
		RequiresSSH: false,
		RequiresPassword: false,
		DefaultPorts: []int{80, 443, 515, 631, 9100},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Printer Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., office-printer",
				Help:        "Unique name for this printer",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "***********00",
				Help:        "IP address of the printer",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{9100}, // Raw printing port
		},
	}
}

func getGenericServerTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "generic-server",
		Name:        "Generic Server",
		Description: "Linux/Unix server",
		Kind:        "server",
		Platform:    "generic",
		DefaultUser: "root",
		RequiresSSH: true,
		RequiresPassword: false,
		DefaultPorts: []int{22, 80, 443},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Server Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., web-server",
				Help:        "Unique name for this server",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "***********0",
				Help:        "IP address or hostname of the server",
			},
			{
				Name:        "user",
				Label:       "SSH Username",
				Type:        "text",
				Required:    true,
				Default:     "root",
				Help:        "SSH username",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path",
				Type:        "file",
				Required:    true,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; change if the server listens on a custom SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{22},
			TestCommands:  []string{"uname -a", "uptime"},
		},
	}
}

func getUnifiTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "unifi",
		Name:        "UniFi Access Point",
		Description: "Ubiquiti UniFi access point or switch",
		Kind:        "access_point",
		Platform:    "unifi",
		DefaultUser: "ubnt",
		RequiresSSH: true,
		RequiresPassword: false,
		DefaultPorts: []int{22, 80, 443, 8080, 8443},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., unifi-ap-1",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "************",
				Help:        "IP address of the UniFi device",
			},
			{
				Name:        "user",
				Label:       "SSH Username",
				Type:        "text",
				Required:    true,
				Default:     "ubnt",
				Help:        "SSH username (usually 'ubnt' for UniFi)",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path",
				Type:        "file",
				Required:    true,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; change if the device listens on a custom SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{22},
			TestCommands:  []string{"cat /etc/version"},
		},
	}
}

func getMikroTikTemplate() config.DeviceTemplate {
	return config.DeviceTemplate{
		ID:          "mikrotik",
		Name:        "MikroTik Router",
		Description: "MikroTik RouterOS device",
		Kind:        "router",
		Platform:    "mikrotik",
		DefaultUser: "admin",
		RequiresSSH: true,
		RequiresPassword: false,
		DefaultPorts: []int{22, 80, 443, 8291},
		Fields: []config.DeviceTemplateField{
			{
				Name:        "name",
				Label:       "Device Name",
				Type:        "text",
				Required:    true,
				Placeholder: "e.g., mikrotik-router",
				Help:        "Unique name for this device",
			},
			{
				Name:        "host",
				Label:       "IP Address",
				Type:        "text",
				Required:    true,
				Placeholder: "************",
				Help:        "IP address of the MikroTik device",
			},
			{
				Name:        "user",
				Label:       "SSH Username",
				Type:        "text",
				Required:    true,
				Default:     "admin",
				Help:        "SSH username (usually 'admin' for MikroTik)",
			},
			{
				Name:        "ssh_key",
				Label:       "SSH Private Key Path",
				Type:        "file",
				Required:    true,
				Placeholder: "/path/to/id_rsa",
				Help:        "Path to SSH private key file",
			},
			{
				Name:        "ssh_port",
				Label:       "SSH Port",
				Type:        "number",
				Required:    false,
				Default:     "22",
				Placeholder: "22",
				Help:        "Defaults to 22; change if the device listens on a custom SSH port.",
			},
		},
		Validation: config.DeviceTemplateValidation{
			RequiredPorts: []int{22},
			TestCommands:  []string{"/system resource print"},
		},
	}
}
