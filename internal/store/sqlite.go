package store

import (
	"database/sql"
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	_ "modernc.org/sqlite"
)

//go:embed schema.sql
var schema string

type Store struct{ DB *sql.DB }

// DeviceRecord represents a device row as stored in the database.
type DeviceRecord struct {
	ID       int64
	Name     string
	Host     string
	Kind     string
	Platform string
	User     string
	SSHKey   string
	Meta     string
}

const pendingDeletionTableDDL = `CREATE TABLE IF NOT EXISTS pending_device_deletions (
  device_id INTEGER PRIMARY KEY,
  delete_at TEXT NOT NULL,
  FOREIGN KEY(device_id) REFERENCES devices(id) ON DELETE CASCADE
);`

const deletedDevicesTableDDL = `CREATE TABLE IF NOT EXISTS deleted_devices (
  name TEXT PRIMARY KEY,
  deleted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);`

type Metric struct {
	ID       int64           `json:"id"`
	DeviceID int64           `json:"device_id"`
	TS       time.Time       `json:"ts"`
	Metric   string          `json:"metric"`
	Value    sql.NullFloat64 `json:"value"`
	Unit     sql.NullString  `json:"unit"`
	Raw      sql.NullString  `json:"raw"`
}

type DeviceLog struct {
	ID       int64     `json:"id"`
	DeviceID int64     `json:"device_id"`
	TS       time.Time `json:"ts"`
	Level    string    `json:"level"`
	Message  string    `json:"message"`
}

type DeviceLogWithMeta struct {
	DeviceLog
	DeviceName string `json:"device_name"`
	DeviceKind string `json:"device_kind"`
	DeviceHost string `json:"device_host"`
}

type DeviceLogFilter struct {
	DeviceIDs  []int64
	DeviceKind string
	Level      string
	Search     string
	Since      time.Time
	Limit      int
}

type SystemLogEntry struct {
	ID       int64     `json:"id"`
	TS       time.Time `json:"ts"`
	Level    string    `json:"level"`
	Category string    `json:"category"`
	Message  string    `json:"message"`
	Context  string    `json:"context"`
}

type SystemLogFilter struct {
	Level    string
	Category string
	Search   string
	Since    time.Time
	Limit    int
}

type PendingDeletion struct {
	DeviceID int64
	DeleteAt time.Time
}

type SSHKeyMeta struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Fingerprint string `json:"fingerprint"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

type SSHKey struct {
	SSHKeyMeta
	Encrypted []byte `json:"-"`
}

func OpenSQLite(path string) (*Store, error) {
	db, err := sql.Open("sqlite", path)
	if err != nil {
		return nil, fmt.Errorf("open sqlite: %w", err)
	}
	if _, err := db.Exec(schema); err != nil {
		return nil, fmt.Errorf("apply schema: %w", err)
	}
	s := &Store{DB: db}
	if err := s.ensurePendingDeletionTable(); err != nil {
		return nil, fmt.Errorf("ensure pending deletions table: %w", err)
	}
	if err := s.ensureDeletedDevicesTable(); err != nil {
		return nil, fmt.Errorf("ensure deleted devices table: %w", err)
	}
	return s, nil
}

func (s *Store) Close() { s.DB.Close() }

func (s *Store) UpsertDevice(name, host, kind, platform, user, sshKey, meta string) (int64, error) {
	if err := s.clearDeletedDevice(name); err != nil {
		return 0, err
	}
	_, err := s.DB.Exec(`INSERT OR IGNORE INTO devices(name,host,kind,platform,user,ssh_key,meta) VALUES(?,?,?,?,?,?,?)`, name, host, kind, platform, user, sshKey, meta)
	if err != nil {
		return 0, err
	}
	var id int64
	err = s.DB.QueryRow(`SELECT id FROM devices WHERE name=?`, name).Scan(&id)
	return id, err
}

// ListDeviceRecords returns a typed view over devices for background workers.
func (s *Store) ListDeviceRecords() ([]DeviceRecord, error) {
	rows, err := s.DB.Query(`SELECT id, name, host, kind, platform, user, ssh_key, meta FROM devices ORDER BY id`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var devices []DeviceRecord
	for rows.Next() {
		var rec DeviceRecord
		var meta sql.NullString
		if err := rows.Scan(&rec.ID, &rec.Name, &rec.Host, &rec.Kind, &rec.Platform, &rec.User, &rec.SSHKey, &meta); err != nil {
			return nil, err
		}
		if meta.Valid {
			rec.Meta = meta.String
		}
		devices = append(devices, rec)
	}
	return devices, rows.Err()
}

func (s *Store) ListDevices() ([]map[string]any, error) {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return nil, err
	}
	rows, err := s.DB.Query(`SELECT d.id,d.name,d.host,d.kind,d.platform,d.user,d.ssh_key,d.meta,pd.delete_at FROM devices d LEFT JOIN pending_device_deletions pd ON pd.device_id=d.id ORDER BY d.id`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []map[string]any
	for rows.Next() {
		var id int64
		var name, host, kind, platform, user, ssh, meta string
		var pending sql.NullString
		if err := rows.Scan(&id, &name, &host, &kind, &platform, &user, &ssh, &meta, &pending); err != nil {
			return nil, err
		}
		entry := map[string]any{
			"id":       id,
			"name":     name,
			"host":     host,
			"kind":     kind,
			"platform": platform,
			"user":     user,
			"ssh_key":  ssh,
			"meta":     meta,
		}
		entry["pending_delete_at"] = func() any {
			if !pending.Valid || pending.String == "" {
				return nil
			}
			if ts, err := parseDeleteAt(pending.String); err == nil {
				return ts.Format(time.RFC3339)
			}
			return pending.String
		}()
		out = append(out, entry)
	}
	return out, rows.Err()
}

func (s *Store) InsertMetric(m Metric) error {
	_, err := s.DB.Exec(`INSERT INTO metrics(device_id, ts, metric, value, unit, raw) VALUES(?,?,?,?,?,?)`,
		m.DeviceID, m.TS.UTC(), m.Metric, m.Value, m.Unit, m.Raw)
	return err
}

func (s *Store) LatestMetric(deviceID int64, metric string) (*Metric, error) {
	row := s.DB.QueryRow(`SELECT id, device_id, ts, metric, value, unit, raw FROM metrics WHERE device_id=? AND metric=? ORDER BY ts DESC LIMIT 1`, deviceID, metric)
	var m Metric
	if err := row.Scan(&m.ID, &m.DeviceID, &m.TS, &m.Metric, &m.Value, &m.Unit, &m.Raw); err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &m, nil
}

func (s *Store) MetricsSince(deviceID int64, metric string, since time.Time, limit int) ([]Metric, error) {
	rows, err := s.DB.Query(`SELECT id, device_id, ts, metric, value, unit, raw FROM metrics WHERE device_id=? AND metric=? AND ts>=? ORDER BY ts LIMIT ?`,
		deviceID, metric, since.UTC(), limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []Metric
	for rows.Next() {
		var m Metric
		if err := rows.Scan(&m.ID, &m.DeviceID, &m.TS, &m.Metric, &m.Value, &m.Unit, &m.Raw); err != nil {
			return nil, err
		}
		out = append(out, m)
	}
	return out, rows.Err()
}

func (s *Store) InsertDeviceLog(deviceID int64, level string, message string) error {
	if level == "" {
		level = "info"
	}
	_, err := s.DB.Exec(`INSERT INTO device_logs(device_id, ts, level, message) VALUES(?,?,?,?)`, deviceID, time.Now().UTC(), level, message)
	return err
}

func (s *Store) InsertSystemLog(level, category, message string, context map[string]any) error {
	level = strings.TrimSpace(strings.ToLower(level))
	if level == "" {
		level = "info"
	}
	category = strings.TrimSpace(category)
	if category == "" {
		category = "general"
	}
	var ctxValue any
	if len(context) > 0 {
		if data, err := json.Marshal(context); err == nil {
			ctxValue = string(data)
		} else {
			ctxValue = nil
		}
	}
	_, err := s.DB.Exec(`INSERT INTO system_logs(ts, level, category, message, context) VALUES(?,?,?,?,?)`,
		time.Now().UTC(), level, category, message, ctxValue)
	return err
}

func (s *Store) RecentSystemLogs(filter SystemLogFilter) ([]SystemLogEntry, error) {
	limit := filter.Limit
	if limit <= 0 {
		limit = 100
	}
	query := `SELECT id, ts, level, category, message, COALESCE(context, '') FROM system_logs`
	var where []string
	var args []any
	if filter.Level != "" {
		where = append(where, "LOWER(level) = LOWER(?)")
		args = append(args, filter.Level)
	}
	if filter.Category != "" {
		where = append(where, "LOWER(category) = LOWER(?)")
		args = append(args, filter.Category)
	}
	if !filter.Since.IsZero() {
		where = append(where, "ts >= ?")
		args = append(args, filter.Since.UTC())
	}
	if filter.Search != "" {
		like := "%" + strings.ToLower(filter.Search) + "%"
		where = append(where, "(LOWER(message) LIKE ? OR LOWER(category) LIKE ?)")
		args = append(args, like, like)
	}
	if len(where) > 0 {
		query += " WHERE " + strings.Join(where, " AND ")
	}
	query += " ORDER BY ts DESC LIMIT ?"
	args = append(args, limit)
	rows, err := s.DB.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var logs []SystemLogEntry
	for rows.Next() {
		var entry SystemLogEntry
		if err := rows.Scan(&entry.ID, &entry.TS, &entry.Level, &entry.Category, &entry.Message, &entry.Context); err != nil {
			return nil, err
		}
		logs = append(logs, entry)
	}
	return logs, rows.Err()
}

func (s *Store) GetSettings() (map[string]string, error) {
	rows, err := s.DB.Query(`SELECT key, value FROM settings`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	settings := make(map[string]string)
	for rows.Next() {
		var key, value string
		if err := rows.Scan(&key, &value); err != nil {
			return nil, err
		}
		settings[key] = value
	}
	return settings, rows.Err()
}

func (s *Store) SetSetting(key, value string) error {
	if strings.TrimSpace(key) == "" {
		return fmt.Errorf("setting key cannot be empty")
	}
	_, err := s.DB.Exec(`INSERT INTO settings(key, value, updated_at) VALUES(?,?,CURRENT_TIMESTAMP)
		ON CONFLICT(key) DO UPDATE SET value=excluded.value, updated_at=CURRENT_TIMESTAMP`, key, value)
	return err
}

func (s *Store) SetSettings(values map[string]string) error {
	if len(values) == 0 {
		return nil
	}
	tx, err := s.DB.Begin()
	if err != nil {
		return err
	}
	stmt, err := tx.Prepare(`INSERT INTO settings(key, value, updated_at) VALUES(?,?,CURRENT_TIMESTAMP)
		ON CONFLICT(key) DO UPDATE SET value=excluded.value, updated_at=CURRENT_TIMESTAMP`)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer stmt.Close()
	for key, value := range values {
		if strings.TrimSpace(key) == "" {
			continue
		}
		if _, err := stmt.Exec(key, value); err != nil {
			tx.Rollback()
			return err
		}
	}
	return tx.Commit()
}

func (s *Store) RecentDeviceLogs(deviceID int64, limit int) ([]DeviceLog, error) {
	if limit <= 0 {
		limit = 25
	}
	rows, err := s.DB.Query(`SELECT id, device_id, ts, level, message FROM device_logs WHERE device_id=? ORDER BY ts DESC LIMIT ?`, deviceID, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var logs []DeviceLog
	for rows.Next() {
		var entry DeviceLog
		if err := rows.Scan(&entry.ID, &entry.DeviceID, &entry.TS, &entry.Level, &entry.Message); err != nil {
			return nil, err
		}
		logs = append(logs, entry)
	}
	return logs, rows.Err()
}

func (s *Store) RecentDeviceLogsFiltered(filter DeviceLogFilter) ([]DeviceLogWithMeta, error) {
	limit := filter.Limit
	if limit <= 0 {
		limit = 100
	}
	query := `SELECT l.id, l.device_id, l.ts, l.level, l.message, d.name, d.kind, d.host
		FROM device_logs l
		INNER JOIN devices d ON d.id = l.device_id`
	var where []string
	var args []any
	if len(filter.DeviceIDs) > 0 {
		placeholders := make([]string, 0, len(filter.DeviceIDs))
		for range filter.DeviceIDs {
			placeholders = append(placeholders, "?")
		}
		where = append(where, fmt.Sprintf("l.device_id IN (%s)", strings.Join(placeholders, ",")))
		for _, id := range filter.DeviceIDs {
			args = append(args, id)
		}
	}
	if filter.DeviceKind != "" {
		where = append(where, "LOWER(d.kind) = LOWER(?)")
		args = append(args, filter.DeviceKind)
	}
	if filter.Level != "" {
		where = append(where, "LOWER(l.level) = LOWER(?)")
		args = append(args, filter.Level)
	}
	if !filter.Since.IsZero() {
		where = append(where, "l.ts >= ?")
		args = append(args, filter.Since.UTC())
	}
	if filter.Search != "" {
		like := "%" + strings.ToLower(filter.Search) + "%"
		where = append(where, "(LOWER(l.message) LIKE ? OR LOWER(d.name) LIKE ? OR LOWER(d.host) LIKE ?)")
		args = append(args, like, like, like)
	}
	if len(where) > 0 {
		query += " WHERE " + strings.Join(where, " AND ")
	}
	query += " ORDER BY l.ts DESC LIMIT ?"
	args = append(args, limit)
	rows, err := s.DB.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var logs []DeviceLogWithMeta
	for rows.Next() {
		var entry DeviceLogWithMeta
		if err := rows.Scan(&entry.ID, &entry.DeviceID, &entry.TS, &entry.Level, &entry.Message, &entry.DeviceName, &entry.DeviceKind, &entry.DeviceHost); err != nil {
			return nil, err
		}
		logs = append(logs, entry)
	}
	return logs, rows.Err()
}

func (s *Store) EnqueueTask(deviceID int64, kind, args, by string) (int64, error) {
	res, err := s.DB.Exec(`INSERT INTO tasks(device_id, kind, args, requested_by, requested_at, status, output) VALUES(?,?,?,?,datetime('now'), 'queued','')`,
		deviceID, kind, args, by)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func (s *Store) UpdateTaskStatus(id int64, status, output string) error {
	_, err := s.DB.Exec(`UPDATE tasks SET status=?, output=? WHERE id=?`, status, output, id)
	return err
}

func (s *Store) ListTasks(deviceID int64, limit int) ([]map[string]any, error) {
	rows, err := s.DB.Query(`SELECT id, device_id, kind, args, requested_by, requested_at, status, output FROM tasks WHERE device_id=? ORDER BY requested_at DESC LIMIT ?`,
		deviceID, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []map[string]any
	for rows.Next() {
		var id, dev int64
		var kind, args, by, at, status, outp string
		if err := rows.Scan(&id, &dev, &kind, &args, &by, &at, &status, &outp); err != nil {
			return nil, err
		}
		out = append(out, map[string]any{"id": id, "device_id": dev, "kind": kind, "args": args, "requested_by": by, "requested_at": at, "status": status, "output": outp})
	}
	return out, rows.Err()
}

// CreateDevice creates a new device and returns its ID
func (s *Store) CreateDevice(name, host, kind, platform, user, sshKey, password, meta string) (int64, error) {
	if err := s.clearDeletedDevice(name); err != nil {
		return 0, err
	}
	res, err := s.DB.Exec(`INSERT INTO devices(name,host,kind,platform,user,ssh_key,meta) VALUES(?,?,?,?,?,?,?)`,
		name, host, kind, platform, user, sshKey, meta)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

// GetDevice retrieves a device by ID
func (s *Store) GetDevice(id int64) (map[string]any, error) {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return nil, err
	}
	row := s.DB.QueryRow(`SELECT d.id,d.name,d.host,d.kind,d.platform,d.user,d.ssh_key,d.meta,pd.delete_at FROM devices d LEFT JOIN pending_device_deletions pd ON pd.device_id=d.id WHERE d.id=?`, id)
	var deviceID int64
	var name, host, kind, platform, user, ssh, meta string
	var pending sql.NullString
	if err := row.Scan(&deviceID, &name, &host, &kind, &platform, &user, &ssh, &meta, &pending); err != nil {
		return nil, err
	}
	resp := map[string]any{
		"id":       deviceID,
		"name":     name,
		"host":     host,
		"kind":     kind,
		"platform": platform,
		"user":     user,
		"ssh_key":  ssh,
		"meta":     meta,
	}
	if pending.Valid && pending.String != "" {
		if ts, err := parseDeleteAt(pending.String); err == nil {
			resp["pending_delete_at"] = ts.Format(time.RFC3339)
		} else {
			resp["pending_delete_at"] = pending.String
		}
	}
	return resp, nil
}

// UpdateDevice updates an existing device
func (s *Store) UpdateDevice(id int64, name, host, kind, platform, user, sshKey, meta string) error {
	_, err := s.DB.Exec(`UPDATE devices SET name=?,host=?,kind=?,platform=?,user=?,ssh_key=?,meta=? WHERE id=?`,
		name, host, kind, platform, user, sshKey, meta, id)
	return err
}

// DeleteDevice removes a device and all its associated data
func (s *Store) DeleteDevice(id int64) error {
	if err := s.ensureDeletedDevicesTable(); err != nil {
		return err
	}
	tx, err := s.DB.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	var name string
	if err := tx.QueryRow(`SELECT name FROM devices WHERE id=?`, id).Scan(&name); err != nil {
		return err
	}

	// Delete associated metrics
	if _, err := tx.Exec(`DELETE FROM metrics WHERE device_id=?`, id); err != nil {
		return err
	}

	// Delete associated tasks
	if _, err := tx.Exec(`DELETE FROM tasks WHERE device_id=?`, id); err != nil {
		return err
	}

	// Clear any pending deletion markers
	if _, err := tx.Exec(`DELETE FROM pending_device_deletions WHERE device_id=?`, id); err != nil {
		return err
	}

	// Delete the device
	if _, err := tx.Exec(`DELETE FROM devices WHERE id=?`, id); err != nil {
		return err
	}

	if err := markDeviceDeletedTx(tx, name); err != nil {
		return err
	}

	return tx.Commit()
}

// DeviceExists checks if a device with the given name already exists
func (s *Store) DeviceExists(name string) (bool, error) {
	var count int
	err := s.DB.QueryRow(`SELECT COUNT(*) FROM devices WHERE name=?`, name).Scan(&count)
	return count > 0, err
}

func (s *Store) ScheduleDeviceDeletion(deviceID int64, deleteAt time.Time) error {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return err
	}
	stamp := deleteAt.UTC().Format(time.RFC3339Nano)
	_, err := s.DB.Exec(`INSERT INTO pending_device_deletions(device_id, delete_at) VALUES(?,?) ON CONFLICT(device_id) DO UPDATE SET delete_at=excluded.delete_at`, deviceID, stamp)
	return err
}

func (s *Store) CancelDeviceDeletion(deviceID int64) (bool, error) {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return false, err
	}
	res, err := s.DB.Exec(`DELETE FROM pending_device_deletions WHERE device_id=?`, deviceID)
	if err != nil {
		return false, err
	}
	n, err := res.RowsAffected()
	if err != nil {
		return false, err
	}
	return n > 0, nil
}

func (s *Store) GetPendingDeviceDeletion(deviceID int64) (*time.Time, error) {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return nil, err
	}
	row := s.DB.QueryRow(`SELECT delete_at FROM pending_device_deletions WHERE device_id=?`, deviceID)
	var raw string
	switch err := row.Scan(&raw); err {
	case nil:
		ts, err := parseDeleteAt(raw)
		if err != nil {
			return nil, err
		}
		return &ts, nil
	case sql.ErrNoRows:
		return nil, nil
	default:
		return nil, err
	}
}

func (s *Store) DueDeviceDeletions(now time.Time) ([]PendingDeletion, error) {
	if err := s.ensurePendingDeletionTable(); err != nil {
		return nil, err
	}
	rows, err := s.DB.Query(`SELECT device_id, delete_at FROM pending_device_deletions WHERE delete_at<=?`, now.UTC().Format(time.RFC3339Nano))
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []PendingDeletion
	for rows.Next() {
		var p PendingDeletion
		var raw string
		if err := rows.Scan(&p.DeviceID, &raw); err != nil {
			return nil, err
		}
		ts, err := parseDeleteAt(raw)
		if err != nil {
			return nil, err
		}
		p.DeleteAt = ts
		out = append(out, p)
	}
	return out, rows.Err()
}

func parseDeleteAt(value string) (time.Time, error) {
	if value == "" {
		return time.Time{}, fmt.Errorf("empty delete_at")
	}
	layouts := []string{
		time.RFC3339Nano,
		time.RFC3339,
		"2006-01-02 15:04:05Z07:00",
		"2006-01-02 15:04:05",
	}
	for _, layout := range layouts {
		if ts, err := time.Parse(layout, value); err == nil {
			return ts.UTC(), nil
		}
	}
	return time.Time{}, fmt.Errorf("cannot parse delete_at: %s", value)
}

func (s *Store) ensurePendingDeletionTable() error {
	_, err := s.DB.Exec(pendingDeletionTableDDL)
	return err
}

func (s *Store) ensureDeletedDevicesTable() error {
	_, err := s.DB.Exec(deletedDevicesTableDDL)
	return err
}

func markDeviceDeletedTx(tx *sql.Tx, name string) error {
	name = strings.TrimSpace(name)
	if name == "" {
		return nil
	}
	_, err := tx.Exec(`INSERT INTO deleted_devices(name, deleted_at) VALUES(?, CURRENT_TIMESTAMP) ON CONFLICT(name) DO UPDATE SET deleted_at=CURRENT_TIMESTAMP`, name)
	return err
}

func (s *Store) clearDeletedDevice(name string) error {
	name = strings.TrimSpace(name)
	if name == "" {
		return nil
	}
	if err := s.ensureDeletedDevicesTable(); err != nil {
		return err
	}
	_, err := s.DB.Exec(`DELETE FROM deleted_devices WHERE name=?`, name)
	return err
}

func (s *Store) IsDeviceDeleted(name string) (bool, error) {
	name = strings.TrimSpace(name)
	if name == "" {
		return false, nil
	}
	if err := s.ensureDeletedDevicesTable(); err != nil {
		return false, err
	}
	var count int
	if err := s.DB.QueryRow(`SELECT COUNT(*) FROM deleted_devices WHERE name=?`, name).Scan(&count); err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *Store) CreateSSHKey(name, fingerprint string, encrypted []byte) (int64, error) {
	res, err := s.DB.Exec(`INSERT INTO ssh_keys(name, fingerprint, encrypted_data) VALUES(?,?,?)`, name, fingerprint, encrypted)
	if err != nil {
		return 0, err
	}
	return res.LastInsertId()
}

func (s *Store) ListSSHKeys() ([]SSHKeyMeta, error) {
	rows, err := s.DB.Query(`SELECT id, name, fingerprint, created_at, updated_at FROM ssh_keys ORDER BY name`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var out []SSHKeyMeta
	for rows.Next() {
		var meta SSHKeyMeta
		if err := rows.Scan(&meta.ID, &meta.Name, &meta.Fingerprint, &meta.CreatedAt, &meta.UpdatedAt); err != nil {
			return nil, err
		}
		out = append(out, meta)
	}
	return out, rows.Err()
}

func (s *Store) GetSSHKey(id int64) (*SSHKey, error) {
	row := s.DB.QueryRow(`SELECT id, name, fingerprint, encrypted_data, created_at, updated_at FROM ssh_keys WHERE id=?`, id)
	var key SSHKey
	if err := row.Scan(&key.ID, &key.Name, &key.Fingerprint, &key.Encrypted, &key.CreatedAt, &key.UpdatedAt); err != nil {
		return nil, err
	}
	return &key, nil
}

func (s *Store) DeleteSSHKey(id int64) error {
	_, err := s.DB.Exec(`DELETE FROM ssh_keys WHERE id=?`, id)
	return err
}
